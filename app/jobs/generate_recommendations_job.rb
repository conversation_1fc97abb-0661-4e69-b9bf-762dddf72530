class GenerateRecommendationsJob < ApplicationJob
  queue_as :default
  retry_on StandardError, wait: 5.seconds, attempts: 3

  def perform(args = {})
    user_id = extract_user_id(args)

    unless user_id
      Rails.logger.error "[GenerateRecommendationsJob] Could not extract user_id from args: #{args.inspect}"
      return
    end

    # If we're not on the job runner instance, delegate the job to the job runner service
    if ENV['JOB_RUNNER_ONLY'] != 'true'
      Rails.logger.info "[GenerateRecommendationsJob] Delegating to job runner service for user #{user_id}"
      
      # First wake up the job runner
      unless JobRunnerService.wake_up_job_runner
        Rails.logger.warn "[GenerateRecommendationsJob] Failed to wake up job runner. Running locally instead."
      else
        job_id = JobRunnerService.run_job('GenerateRecommendationsJob', { user_id: user_id })
        
        if job_id
          Rails.logger.info "[GenerateRecommendationsJob] Successfully delegated to job runner. Job ID: #{job_id}"
          return
        else
          Rails.logger.warn "[GenerateRecommendationsJob] Failed to delegate to job runner. Running locally instead."
        end
      end
    end
    
    user = User.find_by(id: user_id)
    unless user
      Rails.logger.error "[GenerateRecommendationsJob] User #{user_id} not found"
      return
    end

    Rails.logger.info "[GenerateRecommendationsJob] Generating recommendations for user #{user_id}"
    start_time = Time.current

    # Force garbage collection before processing
    GC.start

    begin
      RecommendationService.generate_for_user(user)
      duration = Time.current - start_time
      Rails.logger.info "[GenerateRecommendationsJob] Successfully generated recommendations for user #{user_id} in #{duration.round(2)}s"
    rescue => e
      Rails.logger.error "[GenerateRecommendationsJob] Failed to generate recommendations for user #{user_id}: #{e.message}\n#{e.backtrace.join("\n")}"
    ensure
      # Force garbage collection after processing
      GC.start
    end
  end
  
  # Class method for direct invocation
  def self.generate_recommendations_for_user(args = {})
    # Since this is a class method, we need to instantiate the job to call the instance helper method.
    # Alternatively, the helper could be made a private class method.
    job_instance = new
    user_id = job_instance.send(:extract_user_id, args)

    unless user_id
      Rails.logger.error "[GenerateRecommendationsJob.generate_recommendations_for_user] Could not extract user_id from args: #{args.inspect}"
      return # Or handle error appropriately
    end

    if ENV['JOB_RUNNER_ONLY'] != 'true'
      Rails.logger.info "[GenerateRecommendationsJob] Delegating generate_recommendations_for_user to job runner for user #{user_id}"
      JobRunnerService.wake_up_job_runner
      JobRunnerService.run_specific_job('GenerateRecommendationsJob', 'generate_recommendations_for_user', { user_id: user_id })
    else
      Rails.logger.info "[GenerateRecommendationsJob] Running generate_recommendations_for_user locally for user #{user_id}"
      job_instance.perform(user_id: user_id) # Pass the extracted user_id correctly
    endd
  end

  private

  def extract_user_id(args)
    if args.is_a?(Hash)
      args[:user_id]
    elsif args.is_a?(Array) && args.first.is_a?(Hash)
      args.first[:user_id]
    elsif args.is_a?(Array) && args.first.is_a?(Integer)
      args.first
    elsif args.is_a?(Integer) # Handle case where ID is passed directly
      args
    else
      nil # Or raise an error, depending on desired behavior
    end
  end
end
