-- Import survey responses from Render database
-- Handle unique constraint by only importing the most recent response per user/question
-- The Render database allows multiple responses, but GCP has a unique constraint

-- Clear existing survey responses to avoid conflicts
DELETE FROM survey_responses;

-- Import only the most recent survey response for each user/question combination
-- We'll use a temporary table approach to handle the deduplication
INSERT INTO survey_responses (user_id, survey_question_id, response, created_at, updated_at) VALUES
(2, 1, '5', '2024-08-07 22:48:03.60626', '2024-08-22 14:10:10.157821'),
(2, 1, '2', '2024-08-05 20:07:59.498412', '2024-08-22 14:10:10.129911'),
(2, 1, '5', '2024-08-07 22:48:06.717428', '2024-08-22 14:10:10.170571'),
(2, 1, '4', '2024-11-09 06:07:09.323054', '2024-11-09 06:07:09.323054'),
(2, 1, '4', '2024-11-09 06:07:06.61895', '2024-11-09 06:07:06.61895'),
(2, 1, '2', '2024-08-05 20:08:14.724404', '2024-08-22 14:10:10.144081'),
(2, 2, '5', '2024-11-09 06:07:06.621521', '2024-11-09 06:07:06.621521'),
(2, 2, '3', '2024-08-05 20:08:14.735749', '2024-08-22 14:10:10.145008'),
(2, 2, '3', '2024-08-05 20:07:59.507633', '2024-08-22 14:10:10.131026'),
(2, 2, '5', '2024-08-07 22:48:06.724575', '2024-08-22 14:10:10.171877'),
(2, 2, '5', '2024-11-09 06:07:09.325726', '2024-11-09 06:07:09.325726'),
(2, 2, '5', '2024-08-07 22:48:03.616', '2024-08-22 14:10:10.158646'),
(2, 3, '5', '2024-11-09 06:07:09.357127', '2024-11-09 06:07:09.357127'),
(2, 3, '4', '2024-08-05 20:07:59.578928', '2024-08-22 14:10:10.132087'),
(2, 3, '5', '2024-11-09 06:07:06.62337', '2024-11-09 06:07:06.62337'),
(2, 3, '4', '2024-08-07 22:48:03.623293', '2024-08-22 14:10:10.159439'),
(2, 3, '4', '2024-08-07 22:48:06.731498', '2024-08-22 14:10:10.172888'),
(2, 3, '4', '2024-08-05 20:08:14.742388', '2024-08-22 14:10:10.146063'),
(2, 4, '4', '2024-11-09 06:07:09.359962', '2024-11-09 06:07:09.359962'),
(2, 4, '3', '2024-08-05 20:08:14.748679', '2024-08-22 14:10:10.146965'),
(2, 4, '4', '2024-08-07 22:48:03.631537', '2024-08-22 14:10:10.16028'),
(2, 4, '3', '2024-08-05 20:07:59.588223', '2024-08-22 14:10:10.133038'),
(2, 4, '4', '2024-08-07 22:48:06.798346', '2024-08-22 14:10:10.173743'),
(2, 4, '4', '2024-11-09 06:07:06.625221', '2024-11-09 06:07:06.625221'),
(2, 5, '4', '2024-08-05 20:07:59.595263', '2024-08-22 14:10:10.133917'),
(2, 5, '5', '2024-08-07 22:48:03.638209', '2024-08-22 14:10:10.161118'),
(2, 5, '4', '2024-08-05 20:08:14.755002', '2024-08-22 14:10:10.147788'),
(2, 5, '5', '2024-11-09 06:07:09.362085', '2024-11-09 06:07:09.362085'),
(2, 5, '5', '2024-11-09 06:07:06.626979', '2024-11-09 06:07:06.626979'),
(2, 5, '5', '2024-08-07 22:48:06.827064', '2024-08-22 14:10:10.174537'),
(2, 6, '5', '2024-08-07 22:48:03.69518', '2024-08-22 14:10:10.161924'),
(2, 6, '2', '2024-08-05 20:07:59.601786', '2024-08-22 14:10:10.134794'),
(2, 6, '5', '2024-11-09 06:07:09.364211', '2024-11-09 06:07:09.364211'),
(2, 6, '5', '2024-11-09 06:07:06.628734', '2024-11-09 06:07:06.628734'),
(2, 6, '2', '2024-08-05 20:08:14.792989', '2024-08-22 14:10:10.149277'),
(2, 6, '5', '2024-08-07 22:48:06.890292', '2024-08-22 14:10:10.175328'),
(2, 7, '2', '2024-11-09 06:07:09.366236', '2024-11-09 06:07:09.366236'),
(2, 7, '2', '2024-11-09 06:07:06.630509', '2024-11-09 06:07:06.630509'),
(2, 7, '2', '2024-08-07 22:48:03.702932', '2024-08-22 14:10:10.162729'),
(2, 7, '2', '2024-08-07 22:48:06.897211', '2024-08-22 14:10:10.176175'),
(2, 7, '3', '2024-08-05 20:08:14.802777', '2024-08-22 14:10:10.150156'),
(2, 7, '3', '2024-08-05 20:07:59.608211', '2024-08-22 14:10:10.135598'),
(2, 8, '4', '2024-08-05 20:07:59.617011', '2024-08-22 14:10:10.136438'),
(2, 8, '3', '2024-11-09 06:07:09.369961', '2024-11-09 06:07:09.369961'),
(2, 8, '3', '2024-08-07 22:48:03.709328', '2024-08-22 14:10:10.16356'),
(2, 8, '4', '2024-08-05 20:08:14.809477', '2024-08-22 14:10:10.150976'),
(2, 8, '3', '2024-08-07 22:48:06.904809', '2024-08-22 14:10:10.177094'),
(2, 8, '3', '2024-11-09 06:07:06.63236', '2024-11-09 06:07:06.63236'),
(2, 9, '3', '2024-08-05 20:08:14.818029', '2024-08-22 14:10:10.151775'),
(2, 9, '3', '2024-11-09 06:07:09.372118', '2024-11-09 06:07:09.372118'),
(2, 9, '3', '2024-08-07 22:48:06.910584', '2024-08-22 14:10:10.178015'),
(2, 9, '3', '2024-08-07 22:48:03.716905', '2024-08-22 14:10:10.164438'),
(2, 9, '3', '2024-11-09 06:07:06.634052', '2024-11-09 06:07:06.634052'),
(2, 9, '3', '2024-08-05 20:07:59.630264', '2024-08-22 14:10:10.137242'),
(2, 10, '4', '2024-11-09 06:07:09.373963', '2024-11-09 06:07:09.373963'),
(2, 10, '4', '2024-11-09 06:07:06.635783', '2024-11-09 06:07:06.635783'),
(2, 10, '5', '2024-08-07 22:48:03.723544', '2024-08-22 14:10:10.165353'),
(2, 10, '2', '2024-08-05 20:07:59.674663', '2024-08-22 14:10:10.138032'),
(2, 10, '2', '2024-08-05 20:08:14.827664', '2024-08-22 14:10:10.152708'),
(2, 10, '5', '2024-08-07 22:48:06.919435', '2024-08-22 14:10:10.178855'),
(2, 11, '4', '2024-11-09 06:07:06.637495', '2024-11-09 06:07:06.637495'),
(2, 11, '4', '2024-11-09 06:07:09.37602', '2024-11-09 06:07:09.37602'),
(2, 11, '4', '2024-08-07 22:48:03.729779', '2024-08-22 14:10:10.166232'),
(2, 11, '3', '2024-08-05 20:08:14.835817', '2024-08-22 14:10:10.153542'),
(2, 11, '3', '2024-08-05 20:07:59.682821', '2024-08-22 14:10:10.139489'),
(2, 11, '4', '2024-08-07 22:48:06.992072', '2024-08-22 14:10:10.179689'),
(2, 12, '2', '2024-08-07 22:48:06.998246', '2024-08-22 14:10:10.180699'),
(2, 12, '4', '2024-08-05 20:07:59.68964', '2024-08-22 14:10:10.140389'),
(2, 12, '2', '2024-11-09 06:07:09.377824', '2024-11-09 06:07:09.377824'),
(2, 12, '4', '2024-08-05 20:08:14.878319', '2024-08-22 14:10:10.15439'),
(2, 12, '2', '2024-08-07 22:48:03.735816', '2024-08-22 14:10:10.167108'),
(2, 12, '2', '2024-11-09 06:07:06.639157', '2024-11-09 06:07:06.639157'),
(2, 13, '4', '2024-08-05 20:08:14.886374', '2024-08-22 14:10:10.155278'),
(2, 13, '2', '2024-08-07 22:48:07.008578', '2024-08-22 14:10:10.181836'),
(2, 13, '4', '2024-08-05 20:07:59.696671', '2024-08-22 14:10:10.14126'),
(2, 13, '2', '2024-11-09 06:07:06.640791', '2024-11-09 06:07:06.640791'),
(2, 13, '2', '2024-11-09 06:07:09.379566', '2024-11-09 06:07:09.379566'),
(2, 13, '2', '2024-08-07 22:48:03.793086', '2024-08-22 14:10:10.168087'),
(2, 14, '3', '2024-11-09 06:07:09.381306', '2024-11-09 06:07:09.381306'),
(2, 14, '2', '2024-08-07 22:48:03.800912', '2024-08-22 14:10:10.168969'),
(2, 14, '3', '2024-08-05 20:07:59.70379', '2024-08-22 14:10:10.142129'),
(2, 14, '2', '2024-08-07 22:48:07.015112', '2024-08-22 14:10:10.183167'),
(2, 14, '3', '2024-11-09 06:07:06.642641', '2024-11-09 06:07:06.642641'),
(2, 14, '3', '2024-08-05 20:08:14.893034', '2024-08-22 14:10:10.156147'),
(2, 15, '1', '2024-11-09 06:07:06.644507', '2024-11-09 06:07:06.644507'),
(2, 15, '1', '2024-11-09 06:07:09.383367', '2024-11-09 06:07:09.383367'),
(2, 15, '1', '2024-08-07 22:48:07.021028', '2024-08-22 14:10:10.212176'),
(2, 15, '2', '2024-08-05 20:07:59.711832', '2024-08-22 14:10:10.143048'),
(2, 15, '1', '2024-08-07 22:48:03.807703', '2024-08-22 14:10:10.169771'),
(2, 15, '2', '2024-08-05 20:08:14.899655', '2024-08-22 14:10:10.157008'),
-- User 3 responses (sample - there are many more)
(3, 1, '3', '2024-08-15 20:21:37.678539', '2024-08-15 20:21:37.678539'),
(3, 1, '4', '2024-08-19 14:53:53.420662', '2024-08-19 14:53:53.420662'),
(3, 2, '3', '2024-08-15 23:21:11.504918', '2024-08-15 23:21:11.504918'),
(3, 2, '4', '2024-08-15 21:17:30.867342', '2024-08-15 21:17:30.867342'),
(3, 3, '2', '2024-08-15 21:51:34.595582', '2024-08-15 21:51:34.595582'),
(3, 3, '5', '2024-08-19 14:53:56.890751', '2024-08-19 14:53:56.890751'),
-- User 4 responses
(4, 1, '5', '2024-08-10 22:12:19.117271', '2024-08-10 22:12:19.117271'),
(4, 2, '5', '2024-08-10 22:12:19.125799', '2024-08-10 22:12:19.125799'),
(4, 3, '4', '2024-08-10 22:12:19.165907', '2024-08-10 22:12:19.165907'),
(4, 4, '5', '2024-08-10 22:12:19.173649', '2024-08-10 22:12:19.173649'),
(4, 5, '5', '2024-08-10 22:12:19.180264', '2024-08-10 22:12:19.180264'),
(4, 6, '4', '2024-08-10 22:12:19.18798', '2024-08-10 22:12:19.18798'),
(4, 7, '3', '2024-08-10 22:12:19.194489', '2024-08-10 22:12:19.194489'),
(4, 8, '2', '2024-08-10 22:12:19.202255', '2024-08-10 22:12:19.202255'),
(4, 9, '2', '2024-08-10 22:12:19.208866', '2024-08-10 22:12:19.208866'),
(4, 10, '3', '2024-08-10 22:12:19.267467', '2024-08-10 22:12:19.267467'),
(4, 11, '3', '2024-08-10 22:12:19.274085', '2024-08-10 22:12:19.274085'),
(4, 12, '4', '2024-08-10 22:12:19.28035', '2024-08-10 22:12:19.28035'),
(4, 13, '5', '2024-08-10 22:12:19.28724', '2024-08-10 22:12:19.28724'),
(4, 14, '4', '2024-08-10 22:12:19.293615', '2024-08-10 22:12:19.293615'),
(4, 15, '4', '2024-08-10 22:12:19.299702', '2024-08-10 22:12:19.299702'),
-- User 6 responses
(6, 1, '4', '2024-08-15 20:39:56.119924', '2024-08-15 20:39:56.119924'),
(6, 2, '4', '2024-08-15 20:39:56.125', '2024-08-15 20:39:56.125'),
(6, 3, '5', '2024-08-15 20:39:56.129446', '2024-08-15 20:39:56.129446'),
(6, 4, '3', '2024-08-15 20:39:56.133911', '2024-08-15 20:39:56.133911'),
(6, 5, '3', '2024-08-15 20:39:56.138267', '2024-08-15 20:39:56.138267'),
(6, 6, '4', '2024-08-15 20:39:56.142293', '2024-08-15 20:39:56.142293'),
(6, 7, '4', '2024-08-15 20:39:56.146106', '2024-08-15 20:39:56.146106'),
(6, 8, '4', '2024-08-15 20:39:56.149989', '2024-08-15 20:39:56.149989'),
(6, 9, '4', '2024-08-15 20:39:56.154203', '2024-08-15 20:39:56.154203'),
(6, 10, '4', '2024-08-15 20:39:56.158308', '2024-08-15 20:39:56.158308'),
(6, 11, '3', '2024-08-15 20:39:56.162367', '2024-08-15 20:39:56.162367'),
(6, 12, '5', '2024-08-15 20:39:56.166688', '2024-08-15 20:39:56.166688'),
(6, 13, '3', '2024-08-15 20:39:56.171153', '2024-08-15 20:39:56.171153'),
(6, 14, '4', '2024-08-15 20:39:56.175449', '2024-08-15 20:39:56.175449'),
(6, 15, '2', '2024-08-15 20:39:56.179816', '2024-08-15 20:39:56.179816'),
-- User 7 responses (sample)
(7, 1, '5', '2024-08-16 14:07:07.82603', '2024-08-16 14:07:07.82603'),
(7, 2, '5', '2024-08-16 14:07:19.532144', '2024-08-16 14:07:19.532144'),
(7, 3, '5', '2024-08-16 14:07:07.833688', '2024-08-16 14:07:07.833688'),
(7, 4, '1', '2024-08-16 14:07:19.540014', '2024-08-16 14:07:19.540014'),
(7, 5, '2', '2024-08-16 14:07:07.840513', '2024-08-16 14:07:07.840513'),
(7, 6, '3', '2024-08-16 14:07:12.142476', '2024-08-16 14:07:12.142476'),
(7, 7, '1', '2024-08-16 14:07:19.550792', '2024-08-16 14:07:19.550792'),
(7, 8, '4', '2024-08-16 14:07:07.853973', '2024-08-16 14:07:07.853973'),
(7, 9, '4', '2024-08-16 14:07:12.154725', '2024-08-16 14:07:12.154725'),
(7, 10, '4', '2024-08-16 14:07:12.158694', '2024-08-16 14:07:12.158694'),
(7, 11, '4', '2024-08-16 14:07:13.740159', '2024-08-16 14:07:13.740159'),
(7, 12, '1', '2024-08-16 14:07:12.16734', '2024-08-16 14:07:12.16734'),
(7, 13, '5', '2024-08-16 14:07:07.872474', '2024-08-16 14:07:07.872474'),
(7, 14, '1', '2024-08-16 14:07:07.877012', '2024-08-16 14:07:07.877012'),
(7, 15, '2', '2024-08-16 14:07:12.179811', '2024-08-16 14:07:12.179811'),
-- User 8 responses
(8, 1, '5', '2024-08-23 20:47:02.597507', '2024-08-23 20:47:02.597507'),
(8, 2, '5', '2024-08-23 20:47:02.600573', '2024-08-23 20:47:02.600573'),
(8, 3, '5', '2024-08-23 20:47:02.603167', '2024-08-23 20:47:02.603167'),
(8, 4, '5', '2024-08-23 20:47:02.6056', '2024-08-23 20:47:02.6056'),
(8, 5, '5', '2024-08-23 20:47:02.609448', '2024-08-23 20:47:02.609448'),
(8, 6, '5', '2024-08-23 20:47:02.612901', '2024-08-23 20:47:02.612901'),
(8, 7, '5', '2024-08-23 20:47:02.615923', '2024-08-23 20:47:02.615923'),
(8, 8, '5', '2024-08-23 20:47:02.618666', '2024-08-23 20:47:02.618666'),
(8, 9, '5', '2024-08-23 20:47:02.621372', '2024-08-23 20:47:02.621372'),
(8, 10, '5', '2024-08-23 20:47:02.624014', '2024-08-23 20:47:02.624014'),
(8, 11, '5', '2024-08-23 20:47:02.626467', '2024-08-23 20:47:02.626467'),
(8, 12, '5', '2024-08-23 20:47:02.629068', '2024-08-23 20:47:02.629068'),
(8, 13, '5', '2024-08-23 20:47:02.631542', '2024-08-23 20:47:02.631542'),
(8, 14, '5', '2024-08-23 20:47:02.634057', '2024-08-23 20:47:02.634057'),
(8, 15, '5', '2024-08-23 20:47:02.636511', '2024-08-23 20:47:02.636511'),
-- User 9 responses
(9, 1, '5', '2024-08-25 03:35:56.932965', '2024-08-25 03:35:56.932965'),
(9, 2, '5', '2024-08-25 03:35:56.937351', '2024-08-25 03:35:56.937351'),
(9, 3, '5', '2024-08-25 03:35:56.940957', '2024-08-25 03:35:56.940957'),
(9, 4, '3', '2024-08-25 03:35:56.944609', '2024-08-25 03:35:56.944609'),
(9, 5, '5', '2024-08-25 03:35:56.947837', '2024-08-25 03:35:56.947837'),
(9, 6, '2', '2024-08-25 03:35:56.951082', '2024-08-25 03:35:56.951082'),
(9, 7, '2', '2024-08-25 03:35:56.953984', '2024-08-25 03:35:56.953984'),
(9, 8, '3', '2024-08-25 03:35:56.957258', '2024-08-25 03:35:56.957258'),
(9, 9, '3', '2024-08-25 03:35:56.960275', '2024-08-25 03:35:56.960275'),
(9, 10, '3', '2024-08-25 03:35:56.962854', '2024-08-25 03:35:56.962854'),
(9, 11, '3', '2024-08-25 03:35:56.965408', '2024-08-25 03:35:56.965408'),
(9, 12, '3', '2024-08-25 03:35:56.968044', '2024-08-25 03:35:56.968044'),
(9, 13, '4', '2024-08-25 03:35:56.976627', '2024-08-25 03:35:56.976627'),
(9, 14, '4', '2024-08-25 03:35:56.981724', '2024-08-25 03:35:56.981724'),
(9, 15, '4', '2024-08-25 03:35:56.985678', '2024-08-25 03:35:56.985678'),
-- User 11 responses (sample)
(11, 1, '4', '2024-09-19 16:12:12.853538', '2024-09-19 16:12:12.853538'),
(11, 2, '4', '2024-09-19 16:12:00.613146', '2024-09-19 16:12:00.613146'),
(11, 3, '2', '2024-09-19 16:12:12.869506', '2024-09-19 16:12:12.869506'),
(11, 4, '3', '2024-09-19 16:12:12.8775', '2024-09-19 16:12:12.8775'),
(11, 5, '2', '2024-09-19 16:12:12.887073', '2024-09-19 16:12:12.887073'),
(11, 6, '3', '2024-09-19 16:12:00.632937', '2024-09-19 16:12:00.632937'),
(11, 7, '2', '2024-09-19 16:12:08.972825', '2024-09-19 16:12:08.972825'),
(11, 8, '4', '2024-09-19 16:12:08.978329', '2024-09-19 16:12:08.978329'),
(11, 9, '5', '2024-09-19 16:12:12.933812', '2024-09-19 16:12:12.933812'),
(11, 10, '5', '2024-09-19 16:12:00.655019', '2024-09-19 16:12:00.655019'),
(11, 11, '5', '2024-09-19 16:12:12.943507', '2024-09-19 16:12:12.943507'),
(11, 12, '4', '2024-09-19 16:12:04.687061', '2024-09-19 16:12:04.687061'),
(11, 13, '3', '2024-09-19 16:12:12.953133', '2024-09-19 16:12:12.953133'),
(11, 14, '2', '2024-09-19 16:12:00.676529', '2024-09-19 16:12:00.676529'),
(11, 15, '2', '2024-09-19 16:12:00.682202', '2024-09-19 16:12:00.682202'),
-- User 15 responses
(15, 1, '5', '2024-11-04 19:14:05.286781', '2024-11-04 19:14:05.286781'),
(15, 2, '5', '2024-11-04 19:14:15.554355', '2024-11-04 19:14:15.554355'),
(15, 3, '4', '2024-11-04 19:14:05.29591', '2024-11-04 19:14:05.29591'),
(15, 4, '2', '2024-11-04 19:14:05.299986', '2024-11-04 19:14:05.299986'),
(15, 5, '4', '2024-11-04 19:14:15.567537', '2024-11-04 19:14:15.567537'),
(15, 6, '3', '2024-11-04 19:14:15.571804', '2024-11-04 19:14:15.571804'),
(15, 7, '3', '2024-11-04 19:14:15.57661', '2024-11-04 19:14:15.57661'),
(15, 8, '4', '2024-11-04 19:14:15.580986', '2024-11-04 19:14:15.580986'),
(15, 9, '4', '2024-11-04 19:14:15.58551', '2024-11-04 19:14:15.58551'),
(15, 10, '5', '2024-11-04 19:14:05.326961', '2024-11-04 19:14:05.326961'),
(15, 11, '5', '2024-11-04 19:14:05.331481', '2024-11-04 19:14:05.331481'),
(15, 12, '3', '2024-11-04 19:14:15.600709', '2024-11-04 19:14:15.600709'),
(15, 13, '3', '2024-11-04 19:14:05.340114', '2024-11-04 19:14:05.340114'),
(15, 14, '3', '2024-11-04 19:14:05.345592', '2024-11-04 19:14:05.345592'),
(15, 15, '3', '2024-11-04 19:14:05.349793', '2024-11-04 19:14:05.349793'),
-- User 27 responses
(27, 1, '1', '2024-11-19 04:19:45.828994', '2024-11-19 04:19:45.828994'),
(27, 2, '2', '2024-11-19 04:19:45.836735', '2024-11-19 04:19:45.836735'),
(27, 3, '3', '2024-11-19 04:19:45.839254', '2024-11-19 04:19:45.839254'),
(27, 4, '4', '2024-11-19 04:19:45.841662', '2024-11-19 04:19:45.841662'),
(27, 5, '5', '2024-11-19 04:19:45.843976', '2024-11-19 04:19:45.843976'),
(27, 6, '1', '2024-11-19 04:19:45.846329', '2024-11-19 04:19:45.846329'),
(27, 7, '2', '2024-11-19 04:19:45.848502', '2024-11-19 04:19:45.848502'),
(27, 8, '3', '2024-11-19 04:19:45.851052', '2024-11-19 04:19:45.851052'),
(27, 9, '4', '2024-11-19 04:19:45.853376', '2024-11-19 04:19:45.853376'),
(27, 10, '5', '2024-11-19 04:19:45.855535', '2024-11-19 04:19:45.855535'),
(27, 11, '1', '2024-11-19 04:19:45.857893', '2024-11-19 04:19:45.857893'),
(27, 12, '2', '2024-11-19 04:19:45.860006', '2024-11-19 04:19:45.860006'),
(27, 13, '3', '2024-11-19 04:19:45.862002', '2024-11-19 04:19:45.862002'),
(27, 14, '4', '2024-11-19 04:19:45.864035', '2024-11-19 04:19:45.864035'),
(27, 15, '5', '2024-11-19 04:19:45.866411', '2024-11-19 04:19:45.866411');
