# Use the official Ruby image as a parent image
FROM --platform=linux/amd64 ruby:3.2.1-slim-bullseye AS builder

# Set the platform explicitly for multi-architecture builds
ARG TARGETPLATFORM
RUN echo "Building for platform: $TARGETPLATFORM"

# Install dependencies
RUN apt-get update -qq && \
    apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    gnupg2 \
    git \
    libpq-dev \
    nodejs \
    npm \
    && rm -rf /var/lib/apt/lists/*

# Install Yarn
RUN npm install -g yarn

# Set working directory
WORKDIR /app

# Install gems
COPY Gemfile Gemfile.lock ./
RUN bundle config set --local frozen 'false' && \
    bundle config set --local deployment 'true' && \
    bundle config set --local without 'development test' && \
    bundle lock --add-platform x86_64-linux && \
    bundle lock --add-platform aarch64-linux && \
    bundle install --jobs 4 --retry 3

# Install JavaScript dependencies
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile --production

# Copy application code (excluding .env in production)
COPY . .

# Copy .env file if it exists (for build time only, will be overridden in production)
RUN if [ -f .env ]; then cp .env .env.tmp; fi

# Precompile bootsnap code for faster boot times
RUN bundle exec bootsnap precompile --gemfile app/ lib/

# Precompile assets with dummy values for required environment variables
RUN SECRET_KEY_BASE=dummy \
    ANTHROPIC_API_KEY=dummy \
    OPENAI_API_KEY=dummy \
    RAILS_ENV=production \
    bundle exec rails assets:precompile

# Ensure proper line endings and permissions for binstubs
RUN sed -i 's/\r$//g' bin/* && \
    chmod +x bin/*

# Remove unneeded files (cached packages, temporary files, etc.)
RUN rm -rf /usr/local/bundle/cache/*.gem \
    && { [ -d /usr/local/bundle/gems/ ] && find /usr/local/bundle/gems/ -name "*.c" -delete || true; } \
    && { [ -d /usr/local/bundle/gems/ ] && find /usr/local/bundle/gems/ -name "*.o" -delete || true; }

# Final stage
FROM --platform=linux/amd64 ruby:3.2.1-slim-bullseye

# Install runtime dependencies
RUN apt-get update -qq && \
    apt-get install -y --no-install-recommends \
    libpq5 \
    nodejs \
    && rm -rf /var/lib/apt/lists/*

# Copy application code from builder
WORKDIR /app

# Copy installed gems from builder
COPY --from=builder /usr/local/bundle/ /usr/local/bundle/
# Copy application code
COPY --from=builder /app /app

# Set environment variables
ENV RAILS_ENV=production \
    RAILS_SERVE_STATIC_FILES=true \
    RAILS_LOG_TO_STDOUT=true \
    BUNDLE_PATH=/usr/local/bundle \
    BUNDLE_APP_CONFIG=/usr/local/bundle \
    # Default values for required variables
    ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-dummy} \
    SECRET_KEY_BASE=${SECRET_KEY_BASE:-dummy}

# Create entrypoint script as root first
RUN printf '#!/bin/bash\nset -e\n\n# Ensure scripts are executable\nchmod +x /app/bin/* 2>/dev/null || true\n\n# Start the Rails server on the port specified by Cloud Run\nexec bundle exec rails server -p ${PORT:-3000} -b 0.0.0.0\n' > /app/entrypoint.sh && \
    chmod +x /app/entrypoint.sh && \
    # Ensure Unix line endings
    sed -i 's/\r$//' /app/entrypoint.sh

# Create a non-root user and set permissions
RUN groupadd -r app -g 1000 && \
    useradd -u 1000 -r -g app -d /app -s /sbin/nologin -c "App user" app && \
    chown -R app:app /app

USER app

# Use the PORT environment variable with a default of 3000 for local development
ENV PORT=3000

# Expose the port
EXPOSE $PORT

# Start the application using the entrypoint script
ENTRYPOINT ["/app/entrypoint.sh"]
