# Cloud Build configuration for building and deploying the job runner to Cloud Run

# Set environment variables for the build
substitutions:
  _SERVICE_NAME: cinematch-jobs
  _REGION: us-central1
  _PLATFORM: managed

# Build the Docker image
steps:
  # Build the container image with explicit platform
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '--platform', 'linux/amd64', '-t', 'us-central1-docker.pkg.dev/$PROJECT_ID/cinematch-repo/${_SERVICE_NAME}:latest', '.']
    id: 'Build'

  # Push the container image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'us-central1-docker.pkg.dev/$PROJECT_ID/cinematch-repo/${_SERVICE_NAME}:latest']
    id: 'Push'

  # Deploy container image to Cloud Run with concurrency=1 to ensure jobs don't overlap
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - '${_SERVICE_NAME}'
      - '--image'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/cinematch-repo/${_SERVICE_NAME}:latest'
      - '--region'
      - '${_REGION}'
      - '--platform'
      - '${_PLATFORM}'
      - '--no-allow-unauthenticated'
      - '--service-account=cinematch-runner@$PROJECT_ID.iam.gserviceaccount.com'
      - '--set-env-vars=RAILS_ENV=production,GOOD_JOB_EXECUTION_MODE=async,RAILS_LOG_TO_STDOUT=true,JOB_RUNNER_ONLY=true,MAX_BATCH_SIZE=500'
      - '--add-cloudsql-instances=$PROJECT_ID:${_REGION}:cinematch-db'
      - '--set-secrets=DATABASE_URL=db-url:latest,SECRET_KEY_BASE=secret-key-base:latest,RAILS_MASTER_KEY=rails-master-key:latest,OPENAI_API_KEY=openai-api-key:latest,ANTHROPIC_API_KEY=anthropic-api-key:latest,THEMOVIEDB_KEY=themoviedb-key:latest,OMDB_API_KEY=omdb-api-key:latest,SMTP_USERNAME=smtp-username:latest,SMTP_PASSWORD=smtp-password:latest,SMTP_DOMAIN=smtp-domain:latest,RECAPTCHA_SITE_KEY=recaptcha-site-key:latest,RECAPTCHA_SECRET_KEY=recaptcha-secret-key:latest,GOOGLE_CLIENT_ID=google-client-id:latest,GOOGLE_CLIENT_SECRET=google-client-secret:latest,FACEBOOK_APP_ID=facebook-app-id:latest,FACEBOOK_APP_SECRET=facebook-app-secret:latest,GEMINI_API_KEY=gemini-api-key:latest,TOGETHER_API_KEY=together-api-key:latest,DEEPSPEAK_API_KEY=deepspeak-api-key:latest,ADMIN_EMAIL=admin-email:latest,ADMIN_PASSWORD=admin-password:latest'
      - '--max-instances=1'
      - '--cpu=2'
      - '--memory=2Gi'
      - '--concurrency=1'
      - '--timeout=3600'
    id: 'Deploy'

# Store images in Artifact Registry
images:
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/cinematch-repo/${_SERVICE_NAME}:latest'
