-- Custom import script for user-specific data with schema differences
-- This handles the differences between Render and GCP database schemas

-- Import user_preferences (handling schema differences)
-- Render has: recommended_content_ids (integer[]), recommendations_generated_at (timestamp)
-- GCP has: use_ai (boolean), ai_model (varchar), personality_summary (text),
--          basic_survey_completed (boolean), extended_survey_completed (boolean), extended_survey_in_progress (boolean)

-- Import user_preferences with compatible columns only
INSERT INTO user_preferences (id, user_id, personality_profiles, favorite_genres, created_at, updated_at, deleted_at, disable_adult_content) VALUES
(1, 1, '{}', '{}', '2024-08-02 19:25:49.239771', '2024-08-02 19:25:49.239771', NULL, false),
(2, 2, '{"openness":4.11,"conscientiousness":4.11,"extraversion":2.89,"agreeableness":3.33,"neuroticism":2.22}', '["Mystery"]', '2024-08-02 19:31:34.844659', '2025-05-13 14:29:34.665232', NULL, true),
(3, 3, '{"openness":3.28,"conscientiousness":3.19,"extraversion":3.28,"agreeableness":3.39,"neuroticism":3.35}', '["Crime","Drama","Music","Mystery","Romance"]', '2024-08-04 19:45:27.896778', '2025-05-13 14:29:51.467851', NULL, true),
(4, 4, '{"openness":4.67,"conscientiousness":4.67,"extraversion":2.33,"agreeableness":3.33,"neuroticism":4.33}', '["Action","Adventure","Comedy","Crime","Fantasy","Horror","Science Fiction","Thriller"]', '2024-08-10 22:10:31.276877', '2025-05-13 14:30:13.755627', NULL, false),
(5, 5, '{}', '{}', '2024-08-14 14:44:34.704905', '2024-08-14 14:44:34.704905', NULL, false),
(6, 6, '{"openness":4.33,"conscientiousness":3.33,"extraversion":4.0,"agreeableness":4.0,"neuroticism":3.0}', '["Animation","Crime","Documentary","Horror","Music","Romance","TV Movie"]', '2024-08-15 20:38:02.965767', '2025-05-13 14:30:33.860061', NULL, false),
(7, 7, '{"openness":5.0,"conscientiousness":2.0,"extraversion":3.0,"agreeableness":3.0,"neuroticism":2.67}', '["Comedy","Documentary","Romance","Reality","Talk"]', '2024-08-16 14:05:22.729202', '2025-05-13 14:30:56.359142', NULL, false),
(8, 8, '{"openness":5.0,"conscientiousness":5.0,"extraversion":5.0,"agreeableness":5.0,"neuroticism":5.0}', '["Romance","TV Movie"]', '2024-08-23 20:46:39.220042', '2025-05-13 14:31:10.267523', NULL, false),
(9, 9, '{"openness":5.0,"conscientiousness":3.33,"extraversion":2.67,"agreeableness":3.0,"neuroticism":4.0}', '["Action","Adventure","Animation","Comedy","Documentary","History","Romance","Science Fiction"]', '2024-08-25 03:34:15.626646', '2025-05-13 14:31:32.667176', NULL, false),
(10, 10, '{}', '{}', '2024-09-08 03:28:31.459881', '2025-01-08 16:04:31.045781', '2025-01-08 16:04:31.04573', false),
(11, 11, '{"openness":3.33,"conscientiousness":2.67,"extraversion":3.67,"agreeableness":4.67,"neuroticism":2.33}', '["Documentary","Drama","Family","Mystery","Romance","Thriller","News"]', '2024-09-19 16:10:51.401125', '2025-05-13 14:31:46.554911', NULL, false),
(12, 12, '{}', '{}', '2024-10-19 23:53:32.065563', '2025-01-08 16:04:45.766291', '2025-01-08 16:04:45.766279', false),
(13, 13, '{}', '{}', '2024-10-27 11:03:27.652261', '2024-11-25 15:03:48.263092', '2024-11-25 15:03:48.263079', false),
(14, 14, '{}', '{}', '2024-11-01 11:16:39.546223', '2025-01-08 16:04:43.643018', '2025-01-08 16:04:43.643003', false),
(15, 15, '{"openness":4.67,"conscientiousness":3.0,"extraversion":3.67,"agreeableness":4.33,"neuroticism":3.0}', '["Adventure","Animation","Drama","Fantasy","History","Romance"]', '2024-11-04 19:12:00.782826', '2025-05-13 14:31:55.061125', NULL, false),
(16, 16, '{}', '{}', '2024-11-07 04:11:31.137508', '2024-11-25 15:04:11.83956', '2024-11-25 15:04:11.839551', false),
(17, 17, '{}', '{}', '2024-11-08 11:11:27.048525', '2024-11-25 15:08:11.937417', '2024-11-25 15:08:11.937408', false),
(18, 18, '{}', '{}', '2024-11-09 09:44:00.166594', '2025-01-08 16:04:38.783542', '2025-01-08 16:04:38.783533', false),
(19, 19, '{}', '{}', '2024-11-10 02:37:51.016867', '2025-01-08 16:04:35.573049', '2025-01-08 16:04:35.573038', false),
(20, 20, '{}', '{}', '2024-11-11 14:40:10.505982', '2025-01-08 15:54:47.264786', '2025-01-08 15:54:47.264774', false),
(21, 21, '{}', '{}', '2024-11-13 08:27:13.415241', '2025-01-08 15:54:43.750145', '2025-01-08 15:54:43.750135', false),
(22, 22, '{}', '{}', '2024-11-14 05:28:59.054365', '2025-01-08 15:54:40.768932', '2025-01-08 15:54:40.768918', false),
(23, 23, '{}', '{}', '2024-11-15 02:51:39.067594', '2025-01-08 15:54:37.408785', '2025-01-08 15:54:37.40877', false),
(24, 24, '{}', '{}', '2024-11-16 00:56:41.941709', '2025-01-08 15:54:31.850175', '2025-01-08 15:54:31.850164', false),
(25, 25, '{}', '{}', '2024-11-16 23:18:04.575395', '2025-01-08 15:54:28.867517', '2025-01-08 15:54:28.867506', false),
(26, 26, '{}', '{}', '2024-11-18 03:37:12.642493', '2025-01-08 15:54:19.263674', '2025-01-08 15:54:19.263662', false),
(27, 27, '{"openness":2.0,"conscientiousness":3.33,"extraversion":3.0,"agreeableness":2.67,"neuroticism":4.0}', '["Mystery","Romance","Science Fiction"]', '2024-11-19 04:18:18.367064', '2025-05-13 14:32:05.860892', NULL, true),
(28, 28, '{}', '{}', '2025-03-12 16:37:06.056361', '2025-03-12 16:37:06.056361', NULL, false),
(29, 29, '{}', '{}', '2025-05-31 18:04:35.617879', '2025-05-31 18:04:35.617879', NULL, false);

-- Update the sequence to continue from the highest ID
SELECT setval('user_preferences_id_seq', (SELECT MAX(id) FROM user_preferences));

-- Import watchlist items
INSERT INTO watchlist_items (user_id, source_id, content_type, watched, position, rating, created_at, updated_at) VALUES
(2, '1084736', 'movie', true, 14, 7, '2024-11-09 19:52:17.829727', '2024-11-10 18:35:50.417307'),
(2, '117303', 'tv', true, 8, 8, '2025-03-10 19:55:22.274154', '2025-03-10 19:55:25.557499'),
(2, '124834', 'tv', true, 12, 9, '2025-03-10 01:48:29.120252', '2025-03-10 01:48:33.760734'),
(2, '156933', 'tv', true, 4, NULL, '2025-04-01 17:18:13.803578', '2025-04-01 17:18:14.896637'),
(2, '245775', 'movie', true, 7, 7, '2025-03-10 19:55:58.889081', '2025-03-10 19:56:03.845871'),
(2, '402', 'movie', true, 1, NULL, '2025-04-01 16:19:16.08937', '2025-04-01 21:15:35.021018'),
(2, '508883', 'movie', true, 16, 6, '2024-11-09 19:52:55.69689', '2024-11-09 19:52:59.637307'),
(2, '508965', 'movie', true, 15, 5, '2024-11-09 19:52:28.778364', '2024-11-09 19:52:37.199718'),
(2, '62083', 'tv', true, 10, NULL, '2025-03-10 19:54:48.537988', '2025-03-10 19:57:15.086424'),
(2, '809', 'movie', true, 13, 6, '2024-11-09 19:52:09.660442', '2024-11-10 01:41:50.383205'),
(2, '812', 'movie', false, 1, NULL, '2024-11-27 03:43:29.741258', '2025-04-01 21:15:36.461254'),
(2, '87739', 'tv', true, 11, 8, '2025-03-10 01:48:45.538886', '2025-03-10 01:48:50.459098'),
(2, '9800', 'movie', true, 9, 9, '2025-03-10 19:53:54.818889', '2025-03-10 19:54:01.961929'),
(3, '62177', 'movie', false, 1, NULL, '2025-02-14 14:48:11.817152', '2025-02-14 14:48:11.817152');

-- Note: Survey responses are in a separate file (import_survey_responses.sql) due to volume (824 records)
