#!/bin/bash

# Script to configure watchin.app domain for Google Cloud Run
# Requires Porkbun API credentials

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="watchin.app"
CLOUD_RUN_SERVICE="cinematch-web"
CLOUD_RUN_REGION="us-central1"
PROJECT_ID="cinematch-433023"

echo -e "${GREEN}🌐 Setting up watchin.app domain for Google Cloud Run${NC}"
echo "=================================================="

# Check if Porkbun API credentials are set
if [[ -z "$PORKBUN_API_KEY" || -z "$PORKBUN_SECRET_KEY" ]]; then
    echo -e "${RED}❌ Error: Porkbun API credentials not found${NC}"
    echo "Please set the following environment variables:"
    echo "  export PORKBUN_API_KEY='your_api_key'"
    echo "  export PORKBUN_SECRET_KEY='your_secret_key'"
    echo ""
    echo "Get your API keys from: https://porkbun.com/account/api"
    exit 1
fi

echo -e "${YELLOW}📋 Step 1: Creating domain mapping in Google Cloud Run${NC}"

# Create domain mapping in Cloud Run
gcloud beta run domain-mappings create \
    --service=$CLOUD_RUN_SERVICE \
    --domain=$DOMAIN \
    --region=$CLOUD_RUN_REGION \
    --project=$PROJECT_ID

echo -e "${GREEN}✅ Domain mapping created${NC}"

echo -e "${YELLOW}📋 Step 2: Getting DNS verification record${NC}"

# Get the DNS verification record
VERIFICATION_RECORD=$(gcloud beta run domain-mappings describe $DOMAIN \
    --region=$CLOUD_RUN_REGION \
    --project=$PROJECT_ID \
    --format="value(status.resourceRecords[0].rrdata)" 2>/dev/null || echo "")

if [[ -z "$VERIFICATION_RECORD" ]]; then
    echo -e "${RED}❌ Could not get verification record. Checking domain mapping status...${NC}"
    gcloud beta run domain-mappings describe $DOMAIN \
        --region=$CLOUD_RUN_REGION \
        --project=$PROJECT_ID
    exit 1
fi

echo -e "${GREEN}✅ Verification record: $VERIFICATION_RECORD${NC}"

echo -e "${YELLOW}📋 Step 3: Configuring DNS records via Porkbun API${NC}"

# Function to call Porkbun API
call_porkbun_api() {
    local endpoint="$1"
    local data="$2"
    
    curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$data" \
        "https://api.porkbun.com/api/json/v3/$endpoint"
}

# First, let's see current DNS records
echo "Current DNS records for $DOMAIN:"
CURRENT_RECORDS=$(call_porkbun_api "dns/retrieve/$DOMAIN" "{
    \"secretapikey\": \"$PORKBUN_SECRET_KEY\",
    \"apikey\": \"$PORKBUN_API_KEY\"
}")

echo "$CURRENT_RECORDS" | jq '.' 2>/dev/null || echo "$CURRENT_RECORDS"

# Delete existing A records for the root domain
echo -e "${YELLOW}🗑️  Deleting existing A records for root domain${NC}"
call_porkbun_api "dns/deleteByNameType/$DOMAIN/A/" "{
    \"secretapikey\": \"$PORKBUN_SECRET_KEY\",
    \"apikey\": \"$PORKBUN_API_KEY\"
}" > /dev/null

# Delete existing CNAME records for www
echo -e "${YELLOW}🗑️  Deleting existing CNAME records for www${NC}"
call_porkbun_api "dns/deleteByNameType/$DOMAIN/CNAME/www" "{
    \"secretapikey\": \"$PORKBUN_SECRET_KEY\",
    \"apikey\": \"$PORKBUN_API_KEY\"
}" > /dev/null

# Create CNAME record for root domain pointing to Cloud Run
echo -e "${YELLOW}📝 Creating CNAME record for root domain${NC}"
ROOT_RESULT=$(call_porkbun_api "dns/create/$DOMAIN" "{
    \"secretapikey\": \"$PORKBUN_SECRET_KEY\",
    \"apikey\": \"$PORKBUN_API_KEY\",
    \"name\": \"\",
    \"type\": \"CNAME\",
    \"content\": \"ghs.googlehosted.com\",
    \"ttl\": \"600\"
}")

echo "$ROOT_RESULT" | jq '.' 2>/dev/null || echo "$ROOT_RESULT"

# Create CNAME record for www subdomain
echo -e "${YELLOW}📝 Creating CNAME record for www subdomain${NC}"
WWW_RESULT=$(call_porkbun_api "dns/create/$DOMAIN" "{
    \"secretapikey\": \"$PORKBUN_SECRET_KEY\",
    \"apikey\": \"$PORKBUN_API_KEY\",
    \"name\": \"www\",
    \"type\": \"CNAME\",
    \"content\": \"ghs.googlehosted.com\",
    \"ttl\": \"600\"
}")

echo "$WWW_RESULT" | jq '.' 2>/dev/null || echo "$WWW_RESULT"

echo -e "${GREEN}✅ DNS records configured${NC}"

echo -e "${YELLOW}📋 Step 4: Waiting for DNS propagation${NC}"
echo "This may take a few minutes..."

# Wait for DNS propagation
sleep 30

echo -e "${YELLOW}📋 Step 5: Verifying domain mapping${NC}"

# Check domain mapping status
gcloud beta run domain-mappings describe $DOMAIN \
    --region=$CLOUD_RUN_REGION \
    --project=$PROJECT_ID

echo -e "${GREEN}🎉 Domain setup complete!${NC}"
echo ""
echo "Your domain should be accessible at:"
echo "  • https://$DOMAIN"
echo "  • https://www.$DOMAIN"
echo ""
echo "Note: It may take up to 15 minutes for SSL certificates to be provisioned."
echo "You can check the status with:"
echo "  gcloud run domain-mappings describe $DOMAIN --region=$CLOUD_RUN_REGION"
