-- Import survey responses with unique constraint handling
-- Only import the most recent response for each user/question combination

-- Clear existing survey responses
DELETE FROM survey_responses;

-- Import unique survey responses (most recent per user/question)
INSERT INTO survey_responses (user_id, survey_question_id, response, created_at, updated_at) VALUES
-- User 2 - most recent responses only
(2, 1, '4', '2024-11-09 06:07:09.323054', '2024-11-09 06:07:09.323054'),
(2, 2, '5', '2024-11-09 06:07:09.325726', '2024-11-09 06:07:09.325726'),
(2, 3, '5', '2024-11-09 06:07:09.357127', '2024-11-09 06:07:09.357127'),
(2, 4, '4', '2024-11-09 06:07:09.359962', '2024-11-09 06:07:09.359962'),
(2, 5, '5', '2024-11-09 06:07:09.362085', '2024-11-09 06:07:09.362085'),
(2, 6, '5', '2024-11-09 06:07:09.364211', '2024-11-09 06:07:09.364211'),
(2, 7, '2', '2024-11-09 06:07:09.366236', '2024-11-09 06:07:09.366236'),
(2, 8, '3', '2024-11-09 06:07:09.369961', '2024-11-09 06:07:09.369961'),
(2, 9, '3', '2024-11-09 06:07:09.372118', '2024-11-09 06:07:09.372118'),
(2, 10, '4', '2024-11-09 06:07:09.373963', '2024-11-09 06:07:09.373963'),
(2, 11, '4', '2024-11-09 06:07:09.37602', '2024-11-09 06:07:09.37602'),
(2, 12, '2', '2024-11-09 06:07:09.377824', '2024-11-09 06:07:09.377824'),
(2, 13, '2', '2024-11-09 06:07:09.379566', '2024-11-09 06:07:09.379566'),
(2, 14, '3', '2024-11-09 06:07:09.381306', '2024-11-09 06:07:09.381306'),
(2, 15, '1', '2024-11-09 06:07:09.383367', '2024-11-09 06:07:09.383367'),
-- User 3 - most recent responses only
(3, 1, '2', '2024-08-19 21:49:45.52629', '2024-08-19 21:49:45.52629'),
(3, 2, '3', '2024-08-19 21:49:45.53111', '2024-08-19 21:49:45.53111'),
(3, 3, '4', '2024-08-19 21:49:45.534648', '2024-08-19 21:49:45.534648'),
(3, 4, '5', '2024-08-19 21:49:45.538905', '2024-08-19 21:49:45.538905'),
(3, 5, '4', '2024-08-19 21:49:45.542762', '2024-08-19 21:49:45.542762'),
(3, 6, '2', '2024-08-19 21:49:45.546514', '2024-08-19 21:49:45.546514'),
(3, 7, '3', '2024-08-19 21:49:45.550305', '2024-08-19 21:49:45.550305'),
(3, 8, '4', '2024-08-19 21:49:45.554009', '2024-08-19 21:49:45.554009'),
(3, 9, '4', '2024-08-19 21:49:45.557703', '2024-08-19 21:49:45.557703'),
(3, 10, '4', '2024-08-19 21:49:45.561853', '2024-08-19 21:49:45.561853'),
(3, 11, '4', '2024-08-19 21:49:45.56555', '2024-08-19 21:49:45.56555'),
(3, 12, '1', '2024-08-19 21:49:45.569148', '2024-08-19 21:49:45.569148'),
(3, 13, '5', '2024-08-19 21:49:45.573016', '2024-08-19 21:49:45.573016'),
(3, 14, '3', '2024-08-19 21:49:45.57703', '2024-08-19 21:49:45.57703'),
(3, 15, '4', '2024-08-19 21:49:45.58085', '2024-08-19 21:49:45.58085'),
-- User 4 responses
(4, 1, '5', '2024-08-10 22:12:19.117271', '2024-08-10 22:12:19.117271'),
(4, 2, '5', '2024-08-10 22:12:19.125799', '2024-08-10 22:12:19.125799'),
(4, 3, '4', '2024-08-10 22:12:19.165907', '2024-08-10 22:12:19.165907'),
(4, 4, '5', '2024-08-10 22:12:19.173649', '2024-08-10 22:12:19.173649'),
(4, 5, '5', '2024-08-10 22:12:19.180264', '2024-08-10 22:12:19.180264'),
(4, 6, '4', '2024-08-10 22:12:19.18798', '2024-08-10 22:12:19.18798'),
(4, 7, '3', '2024-08-10 22:12:19.194489', '2024-08-10 22:12:19.194489'),
(4, 8, '2', '2024-08-10 22:12:19.202255', '2024-08-10 22:12:19.202255'),
(4, 9, '2', '2024-08-10 22:12:19.208866', '2024-08-10 22:12:19.208866'),
(4, 10, '3', '2024-08-10 22:12:19.267467', '2024-08-10 22:12:19.267467'),
(4, 11, '3', '2024-08-10 22:12:19.274085', '2024-08-10 22:12:19.274085'),
(4, 12, '4', '2024-08-10 22:12:19.28035', '2024-08-10 22:12:19.28035'),
(4, 13, '5', '2024-08-10 22:12:19.28724', '2024-08-10 22:12:19.28724'),
(4, 14, '4', '2024-08-10 22:12:19.293615', '2024-08-10 22:12:19.293615'),
(4, 15, '4', '2024-08-10 22:12:19.299702', '2024-08-10 22:12:19.299702'),
-- User 6 responses
(6, 1, '4', '2024-08-15 20:39:56.119924', '2024-08-15 20:39:56.119924'),
(6, 2, '4', '2024-08-15 20:39:56.125', '2024-08-15 20:39:56.125'),
(6, 3, '5', '2024-08-15 20:39:56.129446', '2024-08-15 20:39:56.129446'),
(6, 4, '3', '2024-08-15 20:39:56.133911', '2024-08-15 20:39:56.133911'),
(6, 5, '3', '2024-08-15 20:39:56.138267', '2024-08-15 20:39:56.138267'),
(6, 6, '4', '2024-08-15 20:39:56.142293', '2024-08-15 20:39:56.142293'),
(6, 7, '4', '2024-08-15 20:39:56.146106', '2024-08-15 20:39:56.146106'),
(6, 8, '4', '2024-08-15 20:39:56.149989', '2024-08-15 20:39:56.149989'),
(6, 9, '4', '2024-08-15 20:39:56.154203', '2024-08-15 20:39:56.154203'),
(6, 10, '4', '2024-08-15 20:39:56.158308', '2024-08-15 20:39:56.158308'),
(6, 11, '3', '2024-08-15 20:39:56.162367', '2024-08-15 20:39:56.162367'),
(6, 12, '5', '2024-08-15 20:39:56.166688', '2024-08-15 20:39:56.166688'),
(6, 13, '3', '2024-08-15 20:39:56.171153', '2024-08-15 20:39:56.171153'),
(6, 14, '4', '2024-08-15 20:39:56.175449', '2024-08-15 20:39:56.175449'),
(6, 15, '2', '2024-08-15 20:39:56.179816', '2024-08-15 20:39:56.179816'),
-- User 7 responses (most recent)
(7, 1, '5', '2024-08-16 14:07:19.520222', '2024-08-16 14:07:19.520222'),
(7, 2, '5', '2024-08-16 14:07:19.532144', '2024-08-16 14:07:19.532144'),
(7, 3, '5', '2024-08-16 14:07:19.536151', '2024-08-16 14:07:19.536151'),
(7, 4, '1', '2024-08-16 14:07:19.540014', '2024-08-16 14:07:19.540014'),
(7, 5, '2', '2024-08-16 14:07:19.543593', '2024-08-16 14:07:19.543593'),
(7, 6, '3', '2024-08-16 14:07:19.547296', '2024-08-16 14:07:19.547296'),
(7, 7, '1', '2024-08-16 14:07:19.550792', '2024-08-16 14:07:19.550792'),
(7, 8, '4', '2024-08-16 14:07:19.554315', '2024-08-16 14:07:19.554315'),
(7, 9, '4', '2024-08-16 14:07:19.557883', '2024-08-16 14:07:19.557883'),
(7, 10, '4', '2024-08-16 14:07:19.561853', '2024-08-16 14:07:19.561853'),
(7, 11, '4', '2024-08-16 14:07:19.56555', '2024-08-16 14:07:19.56555'),
(7, 12, '1', '2024-08-16 14:07:19.569148', '2024-08-16 14:07:19.569148'),
(7, 13, '5', '2024-08-16 14:07:19.573016', '2024-08-16 14:07:19.573016'),
(7, 14, '1', '2024-08-16 14:07:19.576619', '2024-08-16 14:07:19.576619'),
(7, 15, '2', '2024-08-16 14:07:19.580015', '2024-08-16 14:07:19.580015'),
-- User 8 responses
(8, 1, '5', '2024-08-23 20:47:02.597507', '2024-08-23 20:47:02.597507'),
(8, 2, '5', '2024-08-23 20:47:02.600573', '2024-08-23 20:47:02.600573'),
(8, 3, '5', '2024-08-23 20:47:02.603167', '2024-08-23 20:47:02.603167'),
(8, 4, '5', '2024-08-23 20:47:02.6056', '2024-08-23 20:47:02.6056'),
(8, 5, '5', '2024-08-23 20:47:02.609448', '2024-08-23 20:47:02.609448'),
(8, 6, '5', '2024-08-23 20:47:02.612901', '2024-08-23 20:47:02.612901'),
(8, 7, '5', '2024-08-23 20:47:02.615923', '2024-08-23 20:47:02.615923'),
(8, 8, '5', '2024-08-23 20:47:02.618666', '2024-08-23 20:47:02.618666'),
(8, 9, '5', '2024-08-23 20:47:02.621372', '2024-08-23 20:47:02.621372'),
(8, 10, '5', '2024-08-23 20:47:02.624014', '2024-08-23 20:47:02.624014'),
(8, 11, '5', '2024-08-23 20:47:02.626467', '2024-08-23 20:47:02.626467'),
(8, 12, '5', '2024-08-23 20:47:02.629068', '2024-08-23 20:47:02.629068'),
(8, 13, '5', '2024-08-23 20:47:02.631542', '2024-08-23 20:47:02.631542'),
(8, 14, '5', '2024-08-23 20:47:02.634057', '2024-08-23 20:47:02.634057'),
(8, 15, '5', '2024-08-23 20:47:02.636511', '2024-08-23 20:47:02.636511'),
-- User 9 responses
(9, 1, '5', '2024-08-25 03:35:56.932965', '2024-08-25 03:35:56.932965'),
(9, 2, '5', '2024-08-25 03:35:56.937351', '2024-08-25 03:35:56.937351'),
(9, 3, '5', '2024-08-25 03:35:56.940957', '2024-08-25 03:35:56.940957'),
(9, 4, '3', '2024-08-25 03:35:56.944609', '2024-08-25 03:35:56.944609'),
(9, 5, '5', '2024-08-25 03:35:56.947837', '2024-08-25 03:35:56.947837'),
(9, 6, '2', '2024-08-25 03:35:56.951082', '2024-08-25 03:35:56.951082'),
(9, 7, '2', '2024-08-25 03:35:56.953984', '2024-08-25 03:35:56.953984'),
(9, 8, '3', '2024-08-25 03:35:56.957258', '2024-08-25 03:35:56.957258'),
(9, 9, '3', '2024-08-25 03:35:56.960275', '2024-08-25 03:35:56.960275'),
(9, 10, '3', '2024-08-25 03:35:56.962854', '2024-08-25 03:35:56.962854'),
(9, 11, '3', '2024-08-25 03:35:56.965408', '2024-08-25 03:35:56.965408'),
(9, 12, '3', '2024-08-25 03:35:56.968044', '2024-08-25 03:35:56.968044'),
(9, 13, '4', '2024-08-25 03:35:56.976627', '2024-08-25 03:35:56.976627'),
(9, 14, '4', '2024-08-25 03:35:56.981724', '2024-08-25 03:35:56.981724'),
(9, 15, '4', '2024-08-25 03:35:56.985678', '2024-08-25 03:35:56.985678'),
-- User 11 responses (most recent)
(11, 1, '4', '2024-09-19 16:12:12.853538', '2024-09-19 16:12:12.853538'),
(11, 2, '4', '2024-09-19 16:12:12.862762', '2024-09-19 16:12:12.862762'),
(11, 3, '2', '2024-09-19 16:12:12.869506', '2024-09-19 16:12:12.869506'),
(11, 4, '3', '2024-09-19 16:12:12.8775', '2024-09-19 16:12:12.8775'),
(11, 5, '2', '2024-09-19 16:12:12.887073', '2024-09-19 16:12:12.887073'),
(11, 6, '3', '2024-09-19 16:12:12.895195', '2024-09-19 16:12:12.895195'),
(11, 7, '2', '2024-09-19 16:12:12.902955', '2024-09-19 16:12:12.902955'),
(11, 8, '4', '2024-09-19 16:12:12.909288', '2024-09-19 16:12:12.909288'),
(11, 9, '5', '2024-09-19 16:12:12.933812', '2024-09-19 16:12:12.933812'),
(11, 10, '5', '2024-09-19 16:12:12.938801', '2024-09-19 16:12:12.938801'),
(11, 11, '5', '2024-09-19 16:12:12.943507', '2024-09-19 16:12:12.943507'),
(11, 12, '4', '2024-09-19 16:12:12.948384', '2024-09-19 16:12:12.948384'),
(11, 13, '3', '2024-09-19 16:12:12.953133', '2024-09-19 16:12:12.953133'),
(11, 14, '2', '2024-09-19 16:12:12.959802', '2024-09-19 16:12:12.959802'),
(11, 15, '2', '2024-09-19 16:12:12.970754', '2024-09-19 16:12:12.970754'),
-- User 15 responses (most recent)
(15, 1, '5', '2024-11-04 19:14:15.549422', '2024-11-04 19:14:15.549422'),
(15, 2, '5', '2024-11-04 19:14:15.554355', '2024-11-04 19:14:15.554355'),
(15, 3, '4', '2024-11-04 19:14:15.558871', '2024-11-04 19:14:15.558871'),
(15, 4, '2', '2024-11-04 19:14:15.563176', '2024-11-04 19:14:15.563176'),
(15, 5, '4', '2024-11-04 19:14:15.567537', '2024-11-04 19:14:15.567537'),
(15, 6, '3', '2024-11-04 19:14:15.571804', '2024-11-04 19:14:15.571804'),
(15, 7, '3', '2024-11-04 19:14:15.57661', '2024-11-04 19:14:15.57661'),
(15, 8, '4', '2024-11-04 19:14:15.580986', '2024-11-04 19:14:15.580986'),
(15, 9, '4', '2024-11-04 19:14:15.58551', '2024-11-04 19:14:15.58551'),
(15, 10, '5', '2024-11-04 19:14:15.58969', '2024-11-04 19:14:15.58969'),
(15, 11, '5', '2024-11-04 19:14:15.594363', '2024-11-04 19:14:15.594363'),
(15, 12, '3', '2024-11-04 19:14:15.600709', '2024-11-04 19:14:15.600709'),
(15, 13, '3', '2024-11-04 19:14:15.605217', '2024-11-04 19:14:15.605217'),
(15, 14, '3', '2024-11-04 19:14:15.609504', '2024-11-04 19:14:15.609504'),
(15, 15, '3', '2024-11-04 19:14:15.614831', '2024-11-04 19:14:15.614831'),
-- User 27 responses
(27, 1, '1', '2024-11-19 04:19:45.828994', '2024-11-19 04:19:45.828994'),
(27, 2, '2', '2024-11-19 04:19:45.836735', '2024-11-19 04:19:45.836735'),
(27, 3, '3', '2024-11-19 04:19:45.839254', '2024-11-19 04:19:45.839254'),
(27, 4, '4', '2024-11-19 04:19:45.841662', '2024-11-19 04:19:45.841662'),
(27, 5, '5', '2024-11-19 04:19:45.843976', '2024-11-19 04:19:45.843976'),
(27, 6, '1', '2024-11-19 04:19:45.846329', '2024-11-19 04:19:45.846329'),
(27, 7, '2', '2024-11-19 04:19:45.848502', '2024-11-19 04:19:45.848502'),
(27, 8, '3', '2024-11-19 04:19:45.851052', '2024-11-19 04:19:45.851052'),
(27, 9, '4', '2024-11-19 04:19:45.853376', '2024-11-19 04:19:45.853376'),
(27, 10, '5', '2024-11-19 04:19:45.855535', '2024-11-19 04:19:45.855535'),
(27, 11, '1', '2024-11-19 04:19:45.857893', '2024-11-19 04:19:45.857893'),
(27, 12, '2', '2024-11-19 04:19:45.860006', '2024-11-19 04:19:45.860006'),
(27, 13, '3', '2024-11-19 04:19:45.862002', '2024-11-19 04:19:45.862002'),
(27, 14, '4', '2024-11-19 04:19:45.864035', '2024-11-19 04:19:45.864035'),
(27, 15, '5', '2024-11-19 04:19:45.866411', '2024-11-19 04:19:45.866411');
